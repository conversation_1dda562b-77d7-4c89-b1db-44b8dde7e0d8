<?php

namespace StripeIntegration\Payments\Block\Method;

use Magento\Payment\Block\ConfigurableInfo;

class Invoice extends ConfigurableInfo
{
    protected $_template = 'StripeIntegration_Payments::form/invoice.phtml';

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Payment\Gateway\ConfigInterface $config,
        array $data = []
    ) {
        parent::__construct($context, $config, $data);
    }

    public function getDaysDue()
    {
        return 7;
    }
}
