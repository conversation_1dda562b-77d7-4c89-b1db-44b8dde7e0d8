<?php

namespace StripeIntegration\Payments\Logger;

use Monolog\Logger as MonologLogger;
use Psr\Log\LoggerInterface;

class WebhooksLogger implements LoggerInterface
{
    /**
     * @var MonologLogger
     */
    private $logger;

    public function __construct(
        $name = 'webhooks',
        array $handlers = [],
        array $processors = []
    ) {
        $this->logger = new MonologLogger($name, $handlers, $processors);
    }

    public function emergency($message, array $context = []): void
    {
        $this->logger->emergency($message, $context);
    }

    public function alert($message, array $context = []): void
    {
        $this->logger->alert($message, $context);
    }

    public function critical($message, array $context = []): void
    {
        $this->logger->critical($message, $context);
    }

    public function error($message, array $context = []): void
    {
        $this->logger->error($message, $context);
    }

    public function warning($message, array $context = []): void
    {
        $this->logger->warning($message, $context);
    }

    public function notice($message, array $context = []): void
    {
        $this->logger->notice($message, $context);
    }

    public function info($message, array $context = []): void
    {
        $this->logger->info($message, $context);
    }

    public function debug($message, array $context = []): void
    {
        $this->logger->debug($message, $context);
    }

    public function log($level, $message, array $context = []): void
    {
        $this->logger->log($level, $message, $context);
    }
}
